// 环境配置
window.CONFIG = {
  // 开发环境配置
  development: {
    baseURL: '/api', // 使用本地代理
    timeout: 1000 * 60 * 10,
    uploadURL: 'http://localhost:3000/upload',
    websocketURL: 'ws://localhost:3001',
    // 功能开关
    debug: true,
    mock: true,
    enablePWA: false,
    enableAnalytics: false,
    logLevel: 'debug'
  },

  // 生产环境配置
  production: {
    baseURL: '/api',
    timeout: 10000,
    uploadURL: '/api/upload',
    websocketURL: 'wss://your-domain.com/ws',
    // 功能开关
    debug: false,
    mock: false,
    enablePWA: true,
    enableAnalytics: true,
    logLevel: 'error'
  },

  // 测试环境配置
  test: {
    baseURL: 'http://test-api.example.com/api',
    timeout: 10000,
    uploadURL: 'http://test-api.example.com/upload',
    websocketURL: 'ws://test-api.example.com:3001',
    // 功能开关
    debug: true,
    mock: false,
    enablePWA: false,
    enableAnalytics: false,
    logLevel: 'info'
  }
}

// 获取当前环境配置
window.getConfig = function () {
  // 从HTML的data-env属性获取环境信息
  const env = document.documentElement.getAttribute('data-env') || 'development'
  return window.CONFIG[env] || window.CONFIG.development
}
window.getProjectId = function () {
  return '832a5bf2-fa7e-497b-b849-090c16c02b52'
}
window.getModelUrl = function () {
  return 'http://www.probim.cn:3231'
}
window.getToken = function () {
  return 'D5318751F71E499BB6D6495D88DBEF27D22C22E9A9F57E3BDDD9153C5AE8D37769CB32317F32EB6D0DC734E9E71B5190AB28F87516B6FD532D08248ED2BEA5FD061EDC4C2DDB9943348B52CF58444CDA'
}
window.getSceneUrl = function () {
  // 在开发环境中，public 目录下的文件直接服务在根路径
  // 在生产环境中，也是如此
  return '/scenemanager/#/'
}

